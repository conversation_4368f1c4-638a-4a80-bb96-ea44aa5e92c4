# Makefile for DROID Tools
# Go-based CLI tools for DROID devices with C library backend

# Installation directory
INSTALL_DIR = $(HOME)/bin

# Go program
GO_PROGRAM = droid

# Default target - build Go program
all: $(GO_PROGRAM)

# Build the Go program (depends on C library, always check Go dependencies)
$(GO_PROGRAM): lib/libdroid.a FORCE
	@echo "Formatting Go code..."
	@go fmt ./...
	@echo "Building Go program: $(GO_PROGRAM)"
	go build -ldflags="-s -w" -o $(GO_PROGRAM) ./cmd/droid

# Build the C library
lib/libdroid.a:
	@echo "Building C library..."
	$(MAKE) -C lib

# Install program to ~/bin
install: $(GO_PROGRAM)
	@mkdir -p $(INSTALL_DIR)
	cp $(GO_PROGRAM) $(INSTALL_DIR)/$(GO_PROGRAM)
	@echo "Installed $(GO_PROGRAM) to $(INSTALL_DIR)"

# Clean build artifacts
clean:
	rm -f $(GO_PROGRAM)
	$(MAKE) -C lib clean

# Remove installed program
uninstall:
	rm -f $(INSTALL_DIR)/$(GO_PROGRAM)
	@echo "Removed $(GO_PROGRAM) from $(INSTALL_DIR)"

# Show help
help:
	@echo "Available targets:"
	@echo "  all                    - Build Go program (default)"
	@echo "  droid                  - Build Go program"
	@echo "  install                - Install program to ~/bin"
	@echo "  clean                  - Remove all build artifacts"
	@echo "  uninstall              - Remove installed program"
	@echo "  test                   - Run all tests (unit + smoke)"
	@echo "  test-unit              - Run unit tests only"
	@echo "  test-smoke             - Run smoke tests only"
	@echo "  fmt                    - Format Go code"
	@echo ""
	@echo "DROID Configuration Upload:"
	@echo "  configs/filename.ini   - Upload config file directly (e.g., make configs/palette-control.ini)"
	@echo "  upload CONFIG=file.ini - Upload specific config file"
	@echo "  dry-upload CONFIG=file.ini - Dry-run upload (test without sending)"
	@echo "  wipe                   - Wipe DROID configuration (upload empty file)"
	@echo "  list-devices           - List available MIDI devices"
	@echo "  list-configs           - Show available configuration files"
	@echo ""
	@echo "Examples:"
	@echo "  make configs/palette-control.ini            # Upload palette-control.ini (direct)"
	@echo "  make upload CONFIG=palette-control.ini      # Upload palette-control.ini (variable)"
	@echo "  make dry-upload CONFIG=my-config.ini        # Test upload without sending"
	@echo "  make wipe                                   # Wipe DROID configuration"
	@echo "  help                               - Show this help message"

# Test Go program (unit tests + smoke test)
test: $(GO_PROGRAM) FORCE
	@echo "Running unit tests..."
	@go test ./pkg/... ./cmd/droid/commands -v
	@echo "Running smoke tests..."
	@./$(GO_PROGRAM) help > /dev/null && echo "✓ Go program: Help works"
	@./$(GO_PROGRAM) --version > /dev/null && echo "✓ Go program: Version works"
	@./$(GO_PROGRAM) device list > /dev/null && echo "✓ Go program: Device list works"
	@echo "All tests passed!"

# Run only unit tests
test-unit:
	@echo "Running unit tests..."
	@go test ./pkg/... ./cmd/droid/commands -v

# Run only smoke tests
test-smoke: $(GO_PROGRAM) FORCE
	@echo "Running smoke tests..."
	@./$(GO_PROGRAM) help > /dev/null && echo "✓ Go program: Help works"
	@./$(GO_PROGRAM) --version > /dev/null && echo "✓ Go program: Version works"
	@./$(GO_PROGRAM) device list > /dev/null && echo "✓ Go program: Device list works"
	@echo "Smoke tests passed!"

# Format Go code
fmt:
	@echo "Formatting Go code..."
	@go fmt ./...

# Upload configuration files by path (using Go program)
# Usage: make configs/filename.ini
configs/%.ini: $(GO_PROGRAM)
	@if [ ! -f "$@" ]; then \
		echo "Error: Configuration file $@ not found"; \
		echo "Available files:"; \
		ls configs/*.ini 2>/dev/null || echo "No .ini files in configs/"; \
		exit 1; \
	fi
	@echo "Uploading configuration file: $@"
	./$(GO_PROGRAM) upload --verbose "$@"

# Upload specific configuration file (using Go program)
# Usage: make upload CONFIG=filename.ini
upload: $(GO_PROGRAM)
	@if [ -z "$(CONFIG)" ]; then \
		echo "Error: Please specify CONFIG=filename.ini"; \
		echo "Example: make upload CONFIG=palette-control.ini"; \
		exit 1; \
	fi
	@if [ ! -f "configs/$(CONFIG)" ]; then \
		echo "Error: Configuration file configs/$(CONFIG) not found"; \
		echo "Available files:"; \
		ls configs/*.ini 2>/dev/null || echo "No .ini files in configs/"; \
		exit 1; \
	fi
	@echo "Uploading configuration file: configs/$(CONFIG)"
	./$(GO_PROGRAM) upload --verbose "configs/$(CONFIG)"

# Dry-run upload of specific configuration file (using Go program)
# Usage: make dry-upload CONFIG=filename.ini
dry-upload: $(GO_PROGRAM)
	@if [ -z "$(CONFIG)" ]; then \
		echo "Error: Please specify CONFIG=filename.ini"; \
		echo "Example: make dry-upload CONFIG=palette-control.ini"; \
		exit 1; \
	fi
	@if [ ! -f "configs/$(CONFIG)" ]; then \
		echo "Error: Configuration file configs/$(CONFIG) not found"; \
		echo "Available files:"; \
		ls configs/*.ini 2>/dev/null || echo "No .ini files in configs/"; \
		exit 1; \
	fi
	@echo "Dry-run upload for configuration file: configs/$(CONFIG)"
	./$(GO_PROGRAM) upload --dry-run --verbose "configs/$(CONFIG)"

# Upload empty file (wipe DROID) using Go program
wipe: $(GO_PROGRAM)
	@echo "Wiping DROID configuration (uploading empty file)..."
	@touch .empty_wipe.tmp
	./$(GO_PROGRAM) upload --verbose .empty_wipe.tmp
	@rm -f .empty_wipe.tmp
	@echo "DROID configuration wiped"

# List available MIDI devices using Go program
list-devices: $(GO_PROGRAM)
	./$(GO_PROGRAM) device list

# Show available config files
list-configs:
	@echo "Available configuration files:"
	@if [ -d configs ]; then \
		ls -la configs/*.ini 2>/dev/null || echo "No .ini files found in configs/"; \
	else \
		echo "configs/ directory not found"; \
	fi

# Force target for dependency tracking
FORCE:

# Declare phony targets
.PHONY: all install clean uninstall help test test-unit test-smoke fmt upload dry-upload wipe list-devices list-configs FORCE
